const { expect, assert } = require('chai');
const sinon = require('sinon');
const { app, waitMs } = require('./common');
const { notifs, reset, waitForm, fakeAdminMessaging } = require('./stub');
const { initApp, setFcmToken } = require('./helper/api');
const admin = require('../config/firebase-admin');
const User = require('../models/user');

describe('remove fcmToken on certainErrors', () => {
  beforeEach(async () => {
    // create user 0
    await initApp(0);
  });
  async function getUserFcmTokenData(userId) {
    const user = await User.findOne({ _id: userId }, { fcmToken: 1, fcmTokenUpdatedAt: 1 });
    return {
      fcmToken: user.fcmToken,
      fcmTokenUpdatedAt: user.fcmTokenUpdatedAt,
    };
  }
  it('error on sending single notification', async () => {
    let timeNow = new Date();
    await waitMs(2);
    await setFcmToken(0, 'valid_token');

    // valid token is not removed
    admin.sendNotification(await User.findById(0), null, 'my notification', 'my notification body');
    await waitMs(20);
    let userData = await getUserFcmTokenData(0);
    expect(userData.fcmToken).to.eql('valid_token');
    expect(userData.fcmTokenUpdatedAt).to.be.greaterThan(timeNow);
    expect(userData.fcmTokenUpdatedAt).to.be.lessThan(new Date());

    // invalid tokens removed on certain errors
    await setFcmToken(0, 'invalid_token_0');
    timeNow = new Date();
    await waitMs(2);
    admin.sendNotification(await User.findById(0), null, 'my notification', 'my notification body');
    await waitMs(50);
    userData = await getUserFcmTokenData(0);
    expect(userData.fcmToken).to.eql(null);
    expect(userData.fcmTokenUpdatedAt).to.be.greaterThan(timeNow);
    expect(userData.fcmTokenUpdatedAt).to.be.lessThan(new Date());

    await setFcmToken(0, 'not_reg_token_0');
    timeNow = new Date();
    await waitMs(2);
    admin.sendNotification(await User.findById(0), null, 'my notification', 'my notification body');
    await waitMs(20);
    userData = await getUserFcmTokenData(0);
    expect(userData.fcmToken).to.eql(null);
    expect(userData.fcmTokenUpdatedAt).to.be.greaterThan(timeNow);
    expect(userData.fcmTokenUpdatedAt).to.be.lessThan(new Date());

    // not removed on other errors
    timeNow = new Date();
    await setFcmToken(0, 'unknown_token_0');
    admin.sendNotification(await User.findById(0), null, 'my notification', 'my notification body');
    await waitMs(20);
    userData = await getUserFcmTokenData(0);
    expect(userData.fcmToken).to.eql('unknown_token_0');
    expect(userData.fcmTokenUpdatedAt).to.be.greaterThan(timeNow);
    expect(userData.fcmTokenUpdatedAt).to.be.lessThan(new Date());
  });
});

describe('sendBatchNotifications', () => {
  let sendEachStub;
  let userIncrementMetricsStub;
  let userUpdateManyStub;

  beforeEach(() => {
    reset();
    sendEachStub = sinon.stub(fakeAdminMessaging, 'sendEach');
    userIncrementMetricsStub = sinon.stub(User, 'incrementMetrics').resolves();
    userUpdateManyStub = sinon.stub(User, 'updateMany').resolves();
  });

  afterEach(() => {
    sendEachStub.restore();
    userIncrementMetricsStub.restore();
    userUpdateManyStub.restore();
  });

  it('should handle empty messages array', async () => {
    await admin.sendBatchNotifications([], new Map());
    expect(sendEachStub.called).to.be.false;
  });

  it('should filter out messages without tokens', async () => {
    const messages = [
      { token: 'valid_token', notification: { title: 'Test', body: 'Test' } },
      { notification: { title: 'Test', body: 'Test' } }, // no token
      { token: null, notification: { title: 'Test', body: 'Test' } }, // null token
    ];

    sendEachStub.resolves({
      successCount: 1,
      failureCount: 0,
      responses: [{ success: true, messageId: 'msg1' }]
    });

    await admin.sendBatchNotifications(messages, new Map());
    
    expect(sendEachStub.calledOnce).to.be.true;
    expect(sendEachStub.getCall(0).args[0]).to.have.length(1);
    expect(sendEachStub.getCall(0).args[0][0].token).to.equal('valid_token');
  });

  it('should send batch notifications successfully', async () => {
    const messages = [
      { token: 'token1', notification: { title: 'Test1', body: 'Body1' } },
      { token: 'token2', notification: { title: 'Test2', body: 'Body2' } },
    ];

    const userIdTokenMap = new Map([
      ['token1', 'user1'],
      ['token2', 'user2'],
    ]);

    sendEachStub.resolves({
      successCount: 2,
      failureCount: 0,
      responses: [
        { success: true, messageId: 'msg1' },
        { success: true, messageId: 'msg2' }
      ]
    });

    await admin.sendBatchNotifications(messages, userIdTokenMap, null, 'testMetric');

    expect(sendEachStub.calledOnce).to.be.true;
    expect(sendEachStub.getCall(0).args[0]).to.have.length(2);
    expect(userIncrementMetricsStub.callCount).to.equal(2);
    expect(userIncrementMetricsStub.getCall(0).args).to.deep.equal(['user1', ['numNotifications', 'testMetric']]);
    expect(userIncrementMetricsStub.getCall(1).args).to.deep.equal(['user2', ['numNotifications', 'testMetric']]);
  });

  it('should handle failed notifications and remove invalid tokens', async () => {
    const messages = [
      { token: 'valid_token', notification: { title: 'Test1', body: 'Body1' } },
      { token: 'invalid_token', notification: { title: 'Test2', body: 'Body2' } },
    ];

    const userIdTokenMap = new Map([
      ['valid_token', 'user1'],
      ['invalid_token', 'user2'],
    ]);

    sendEachStub.resolves({
      successCount: 1,
      failureCount: 1,
      responses: [
        { success: true, messageId: 'msg1' },
        { success: false, error: { code: 'messaging/invalid-registration-token' } }
      ]
    });

    await admin.sendBatchNotifications(messages, userIdTokenMap, null, 'testMetric');

    expect(sendEachStub.calledOnce).to.be.true;
    expect(userIncrementMetricsStub.callCount).to.equal(2); // 1 success + 1 error
    expect(userIncrementMetricsStub.getCall(0).args).to.deep.equal(['user1', ['numNotifications', 'testMetric']]);
    expect(userIncrementMetricsStub.getCall(1).args).to.deep.equal(['user2', ['numNotificationErrors']]);
    
    // Should remove invalid token
    expect(userUpdateManyStub.calledOnce).to.be.true;
    expect(userUpdateManyStub.getCall(0).args[0]).to.deep.equal({ fcmToken: { $in: ['invalid_token'] } });
  });

  it('should handle delete action without incrementing notification metrics', async () => {
    const messages = [
      { token: 'token1', notification: { title: 'Test1', body: 'Body1' } },
    ];

    const userIdTokenMap = new Map([
      ['token1', 'user1'],
    ]);

    const data = { action: 'delete' };

    sendEachStub.resolves({
      successCount: 1,
      failureCount: 0,
      responses: [{ success: true, messageId: 'msg1' }]
    });

    await admin.sendBatchNotifications(messages, userIdTokenMap, data, 'testMetric');

    expect(sendEachStub.calledOnce).to.be.true;
    expect(userIncrementMetricsStub.called).to.be.false; // No metrics should be incremented for delete actions
  });

  it('const { , reset } = require('./stub');
', async () => {
    // Create messages that exceed MAX_FCM_BATCH_SIZE (500)
    const messages = [];
    const userIdTokenMap = new Map();
    
    for (let i = 0; i < 600; i++) {
      const token = `token${i}`;
      messages.push({ token, notification: { title: `Test${i}`, body: `Body${i}` } });
      userIdTokenMap.set(token, `user${i}`);
    }

    sendEachStub.resolves({
      successCount: 500,
      failureCount: 0,
      responses: Array(500).fill({ success: true, messageId: 'msg' })
    });

    await admin.sendBatchNotifications(messages, userIdTokenMap, null, 'testMetric');

    // Should be called twice: once for first 500, once for remaining 100
    expect(sendEachStub.callCount).to.equal(2);
    expect(sendEachStub.getCall(0).args[0]).to.have.length(500);
    expect(sendEachStub.getCall(1).args[0]).to.have.length(100);
  });

  it('should handle sendEach error gracefully', async () => {
    const messages = [
      { token: 'token1', notification: { title: 'Test1', body: 'Body1' } },
    ];

    const userIdTokenMap = new Map([
      ['token1', 'user1'],
    ]);

    sendEachStub.rejects(new Error('Network error'));

    await admin.sendBatchNotifications(messages, userIdTokenMap, null, 'testMetric');

    expect(sendEachStub.calledOnce).to.be.true;
    expect(userIncrementMetricsStub.calledOnce).to.be.true;
    expect(userIncrementMetricsStub.getCall(0).args).to.deep.equal(['user1', ['numNotificationErrors']]);
  });

  it('should work without userIdTokenMap', async () => {
    const messages = [
      { token: 'token1', notification: { title: 'Test1', body: 'Body1' } },
    ];

    sendEachStub.resolves({
      successCount: 1,
      failureCount: 0,
      responses: [{ success: true, messageId: 'msg1' }]
    });

    await admin.sendBatchNotifications(messages, new Map());

    expect(sendEachStub.calledOnce).to.be.true;
    expect(userIncrementMetricsStub.called).to.be.false; // No user mapping, so no metrics
  });
});

